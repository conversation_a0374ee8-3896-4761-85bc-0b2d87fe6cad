import { validEmailsByComma } from '../emailHeadHelper';

describe('#validEmailsByComma', () => {
  it('returns true when empty string is passed', () => {
    expect(validEmailsByComma('')).toEqual(true);
  });
  it('returns true when valid emails separated by comma is passed', () => {
    expect(validEmailsByComma('<EMAIL>,<EMAIL>')).toEqual(true);
  });
  it('returns false when one of the email passed is invalid', () => {
    expect(validEmailsByComma('<EMAIL>,pova.da')).toEqual(false);
  });
  it('strips spaces between emails before validating', () => {
    expect(validEmailsByComma('<EMAIL>  , <EMAIL>')).toEqual(true);
  });
});
