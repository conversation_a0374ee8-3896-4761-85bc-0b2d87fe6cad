@mixin label-multiselect-hover {
  &::after {
    @apply text-n-brand;
  }

  &:hover {
    @apply bg-n-slate-3;

    &::after {
      @apply text-n-blue-text;
    }
  }
}

.multiselect {
  &:not(.no-margin) {
    @apply mb-4;
  }

  &.invalid .multiselect__tags {
    @apply border-0 outline outline-1 outline-n-ruby-8 dark:outline-n-ruby-8 hover:outline-n-ruby-9 dark:hover:outline-n-ruby-9 disabled:outline-n-ruby-8 dark:disabled:outline-n-ruby-8;
  }

  &.multiselect--disabled {
    @apply opacity-50  rounded-lg cursor-not-allowed pointer-events-auto;

    .multiselect__select {
      @apply cursor-not-allowed bg-transparent rounded-lg;
    }
  }

  .multiselect--active {
    > .multiselect__tags {
      @apply outline-n-blue-border;
    }
  }

  .multiselect__select {
    @apply min-h-[2.875rem] p-0 right-0 top-0;

    &::before {
      @apply right-0;
    }
  }

  .multiselect__content-wrapper {
    @apply bg-n-alpha-black2 text-n-slate-12 backdrop-blur-[100px] border-0 border-none outline outline-1 outline-n-weak rounded-b-lg;
  }

  .multiselect__content {
    @apply max-w-full;

    .multiselect__option {
      @apply text-sm font-normal flex justify-between items-center;

      span {
        @apply inline-block overflow-hidden text-ellipsis whitespace-nowrap w-fit;
      }

      p {
        @apply mb-0;
      }

      &::after {
        @apply bottom-0 flex items-center justify-center text-center relative px-1 leading-tight;
      }

      &.multiselect__option--highlight {
        @apply bg-n-alpha-black2 text-n-slate-12;
      }

      &.multiselect__option--highlight:hover {
        @apply bg-n-brand/10 text-n-slate-12;

        &::after {
          @apply bg-transparent text-center text-n-slate-12;
        }
      }

      &.multiselect__option--highlight::after {
        @apply bg-transparent text-n-slate-12;
      }

      &.multiselect__option--selected {
        @apply bg-n-brand/20 text-n-slate-12;

        &::after {
          @apply bg-transparent;
        }

        &.multiselect__option--highlight:hover {
          @apply bg-transparent;

          &::after:hover {
            @apply text-n-slate-12 bg-transparent;
          }
        }
      }
    }
  }

  .multiselect__tags {
    @apply bg-n-alpha-black2 border-0 grid items-center w-full border-none outline-1 outline outline-n-weak hover:outline-n-slate-6 m-0 min-h-[2.875rem] rounded-lg pt-0;

    input {
      @apply border-0 border-none bg-transparent dark:bg-transparent text-n-slate-12 placeholder:text-n-slate-10;
    }
  }

  .multiselect__spinner {
    background-color: transparent;
  }

  .multiselect__tags-wrap {
    @apply inline-block leading-none mt-1;
  }

  .multiselect__placeholder {
    @apply text-n-slate-10 font-normal pt-3;
  }

  .multiselect__tag {
    @apply bg-n-alpha-white mt-1 text-n-slate-12 pr-6 pl-2.5 py-1.5;
  }

  .multiselect__tag-icon {
    @include label-multiselect-hover;
  }

  .multiselect__input {
    @apply text-sm h-[2.875rem] mb-0 p-0 shadow-none border-transparent hover:border-transparent hover:shadow-none focus:border-transparent focus:shadow-none active:border-transparent active:shadow-none;
  }

  .multiselect__single {
    @apply bg-transparent text-n-slate-12 inline-block mb-0 py-3 px-2.5 overflow-hidden whitespace-nowrap text-ellipsis;
  }
}

.sidebar-labels-wrap {
  &.has-edited,
  &:hover {
    .multiselect {
      @apply cursor-pointer;
    }
  }

  .multiselect {
    > .multiselect__select {
      @apply invisible;
    }

    > .multiselect__tags {
      @apply outline-transparent;
    }

    &.multiselect--active > .multiselect__tags {
      @apply outline-n-blue-border;
    }
  }
}

.multiselect-wrap--small {
  // To be removed one SLA reports date picker is created
  &.tiny {
    .multiselect.no-margin {
      @apply min-h-[32px];
    }

    .multiselect__select {
      @apply min-h-[32px] h-8;

      &::before {
        @apply top-[60%];
      }
    }

    .multiselect__tags {
      @apply min-h-[32px] max-h-[32px];

      .multiselect__single {
        @apply pt-1 pb-1;
      }
    }
  }

  .multiselect__tags,
  .multiselect__input,
  .multiselect {
    @apply text-n-slate-12 rounded-lg text-sm  min-h-[2.5rem];
  }

  .multiselect__input {
    @apply h-[2.375rem] min-h-[2.375rem];
  }

  .multiselect__single {
    @apply items-center flex m-0 text-sm max-h-[2.375rem] bg-transparent text-n-slate-12 py-3 px-0.5;
  }

  .multiselect__placeholder {
    @apply m-0 py-2 px-0.5;
  }

  .multiselect__tag {
    @apply py-[6px] my-[1px];
  }

  .multiselect__select {
    @apply min-h-[2.5rem];
  }

  .multiselect--disabled .multiselect__current,
  .multiselect--disabled .multiselect__select {
    @apply bg-transparent;
  }
}

.multiselect--disabled {
  background-color: rgba(var(--black-alpha-2)) !important;

  .multiselect__tags {
    @apply hover:outline-n-weak;
  }
}

.multiselect--active {
  .multiselect__select::before {
    @apply top-[62%];
  }
}

.multiselect__select::before {
  top: 60% !important;
}

.multiselect-wrap--medium {
  .multiselect__tags,
  .multiselect__input {
    @apply items-center flex;
  }

  .multiselect__tags,
  .multiselect__input,
  .multiselect {
    @apply bg-n-alpha-black2 text-n-slate-12 text-sm h-12 min-h-[3rem];
  }

  .multiselect__input {
    @apply h-[2.875rem] min-h-[2.875rem];
    margin-bottom: 0 !important;
  }

  .multiselect__single {
    @apply items-center flex m-0 text-sm py-1 px-0.5 bg-transparent text-n-slate-12;
  }

  .multiselect__placeholder {
    @apply m-0 py-1 px-0.5;
  }

  .multiselect__select {
    @apply min-h-[3rem];
  }

  .multiselect--disabled .multiselect__current,
  .multiselect--disabled .multiselect__select {
    @apply bg-transparent;
  }

  .multiselect__tags-wrap {
    @apply flex-shrink-0;
  }
}
