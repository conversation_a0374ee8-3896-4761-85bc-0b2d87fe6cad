.cell-label {
  &:hover {
    a {
      color: $action-color;
    }

    svg {
      fill: $action-color;
      transform: rotate(180deg);
    }
  }

  a {
    color: inherit;
    display: inline-block;
    transition: color $base-duration $base-timing;
    width: 100%;
  }
}

.cell-label--asc,
.cell-label--desc {
  font-weight: $font-weight-medium;
}

.cell-label__sort-indicator {
  float: right;
  margin-left: 5px;

  svg {
    fill: $hint-grey;
    height: 13px;
    transition: transform $base-duration $base-timing;
    width: 13px;
  }
}

.cell-label__sort-indicator--desc {
  transform: rotate(180deg);
}

.cell-data--number,
.cell-label--number {
  text-align: right;
}

.cell-data__secret-field {
  align-items: center;
  color: $hint-grey;
  display: flex;

  span {
    flex: 0 0 auto;
  }

  [data-secret-toggler],
  [data-secret-copier] {
    background: transparent;
    border: 0;
    color: inherit;
    margin-left: 0.5rem;
    padding: 0;

    svg {
      fill: currentColor;
      height: 1.25rem;
      width: 1.25rem;
    }
  }
}
