# #
# #
# # Linux nightly installer action
# # This action will try to install and setup
# # chatwoot on an Ubuntu 22.04 machine using
# # the linux installer script.
# #
# # This is set to run daily at midnight.
# #

name: Run Linux nightly installer
on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

jobs:
  nightly:
    runs-on: ubuntu-24.04
    steps:

    - name: get installer
      run: |
        wget https://get.chatwoot.app/linux/install.sh
        chmod +x install.sh
        #fix for postgtres not starting automatically in gh action env
        sed -i '/function configure_db() {/a sudo service postgresql start' install.sh

    - name: create input file
      run: |
        echo "no" > input
        echo "yes" >> input

    - name: Run the installer
      run: |
        sudo ./install.sh --install < input

        #  disabling http verify for now as http
        #  access to port 3000 fails in gh action env
        #    - name: Verify
        #      if: always()
        #      run: |
        #        sudo netstat -ntlp | grep 3000
        #        sudo systemctl restart chatwoot.target
        #        curl http://localhost:3000/api

    - name: Upload chatwoot setup log file as an artifact
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: chatwoot-setup-log-file
        path: /var/log/chatwoot-setup.log
