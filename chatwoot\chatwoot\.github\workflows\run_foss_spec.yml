name: Run Chatwoot CE spec
on:
  push:
    branches:
      - develop
      - master
  pull_request:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-22.04
    services:
      postgres:
        image: pgvector/pgvector:pg15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: ''
          POSTGRES_DB: postgres
          POSTGRES_HOST_AUTH_METHOD: trust
        ports:
          - 5432:5432
        # needed because the postgres container does not provide a healthcheck
        # tmpfs makes DB faster by using RAM
        options: >-
          --mount type=tmpfs,destination=/var/lib/postgresql/data
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports:
          - 6379:6379
        options: --entrypoint redis-server

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}

      - uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true # runs 'bundle install' and caches installed gems automatically

      - uses: actions/setup-node@v4
        with:
          node-version: 23
          cache: 'pnpm'

      - name: Install pnpm dependencies
        run: pnpm i

      - name: Strip enterprise code
        run: |
          rm -rf enterprise
          rm -rf spec/enterprise

      - name: Create database
        run: bundle exec rake db:create

      - name: Seed database
        run: bundle exec rake db:schema:load

      - name: Run frontend tests
        run: pnpm run test:coverage

      # Run rails tests
      - name: Run backend tests
        run: |
          bundle exec rspec --profile=10 --format documentation
        env:
          NODE_OPTIONS: --openssl-legacy-provider

      - name: Upload rails log folder
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: rails-log-folder
          path: log
