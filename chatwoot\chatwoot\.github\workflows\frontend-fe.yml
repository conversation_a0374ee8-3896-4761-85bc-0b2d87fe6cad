name: Frontend Lint & Test

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop

jobs:
  test:
    runs-on: ubuntu-22.04

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}

      - uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true

      - uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 23
          cache: 'pnpm'

      - name: Install pnpm dependencies
        run: pnpm install --frozen-lockfile

      - name: Run eslint
        run: pnpm run eslint

      - name: Run frontend tests with coverage
        run: |
          mkdir -p coverage
          pnpm run test:coverage
